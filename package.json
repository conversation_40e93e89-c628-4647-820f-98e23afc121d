{"name": "browseforme", "displayName": "browseforme", "version": "0.0.1", "description": "帮我刷帖", "author": "kongkongye", "scripts": {"dev": "rm -rf build && plasmo dev", "build": "plasmo build", "package": "plasmo package"}, "dependencies": {"@mlc-ai/web-llm": "^0.2.79", "@plasmohq/messaging": "^0.7.1", "@plasmohq/redux-persist": "^6.1.0", "@plasmohq/storage": "^1.15.0", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-label": "^2.1.2", "@reduxjs/toolkit": "^2.6.1", "framer-motion": "^12.5.0", "next-themes": "^0.4.6", "plasmo": "0.90.3", "react": "18.2.0", "react-dom": "18.2.0", "react-redux": "^9.2.0", "redux-persist-webextension-storage": "^1.0.2", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "4.1.1", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-toast": "^1.2.6", "@tailwindcss/typography": "^0.5.16", "@types/chrome": "0.0.258", "@types/node": "20.11.5", "@types/node-fetch": "^2.6.12", "@types/react": "18.2.48", "@types/react-dom": "18.2.18", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.476.0", "postcss": "^8.5.3", "prettier": "3.2.4", "tailwind-merge": "^3.0.2", "tailwind-scrollbar-hide": "^2.0.0", "tailwindcss": "^3.3.0", "typescript": "5.3.3"}, "manifest": {"host_permissions": ["https://*/*", "http://*/*"], "permissions": ["activeTab", "tabs", "storage", "scripting", "sidePanel"], "web_accessible_resources": [{"resources": ["*.css", "demo_data/*.json"], "matches": ["<all_urls>"]}], "content_security_policy": {"extension_pages": "script-src 'self' 'wasm-unsafe-eval'; object-src 'self';"}, "options_page": "options.html"}}