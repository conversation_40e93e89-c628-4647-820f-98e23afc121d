import { Provider } from "react-redux"
import { PersistGate } from "@plasmohq/redux-persist/integration/react"
import { ThemeProvider } from "~components/ui/theme-provider"
import { persistor, store } from "~store/store"
import LLMChat from "~components/llm/LLMChat"
import "./style.css"

// 子组件，在Redux Provider内部使用
function SidePanelContent() {
  return (
    <div className="h-screen overflow-auto">
      <LLMChat />
    </div>
  )
}

function SidePanel() {
  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
          <SidePanelContent />
        </ThemeProvider>
      </PersistGate>
    </Provider>
  )
}

export default SidePanel