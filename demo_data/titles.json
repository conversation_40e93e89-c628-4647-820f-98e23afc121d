[{"id": 100, "title": "BitTrans - 使用动态二维码传数据，离线、安全、开源"}, {"id": 101, "title": "颠覆传统：忘掉“学”英语，开始“接触”英语！一键给你的浏览器装一个“英语环境”生成器"}, {"id": 102, "title": "#1 Flux AI：革命性的 AI 图像生成与编辑平台"}, {"id": 103, "title": "基于 LangChain 的开源 GPT 向量 + 知识数据库，帮助个人或企业实现自己的专属 AI 问答助手"}, {"id": 104, "title": "ImgMCP 更新：新增创作画布，支持新模型（Midjourney V1 / Seedream 3.0 等）"}, {"id": 105, "title": "SolVanityCL：快速生成你想要的 Sol 地址（靓号）"}, {"id": 106, "title": "我给 Poixe 社区搭建的 Discourse 论坛，加入了 AI 机器人 [图图]"}, {"id": 107, "title": "阿里云 ESA 边缘函数转发代理 docker registry"}, {"id": 108, "title": "可能是最优雅的免费无广的在线设备检测工具网站了"}, {"id": 109, "title": "[原创] Neopan-网盘批处理助手，支持夸克网盘&百度网盘"}, {"id": 110, "title": "拒绝 AI 蓝紫配色，看的有点难受了，嘴遁了一个淡绿色的。哈哈哈。"}, {"id": 111, "title": "一行代码，直连 V2EX"}, {"id": 112, "title": "LaPluma : 一个轻盈的 Go 数据流处理库"}, {"id": 113, "title": "[免费] 做了一款 mac 上的桌面时钟 TimePass"}, {"id": 114, "title": "朋友们，新产品上线，求帮忙投一票，谢谢啦～🙏"}, {"id": 115, "title": "Vibe 了一个 _代码质量可能很高_ 脚手架， Gemini 给出的项目质量评估是 9.5/10..."}, {"id": 116, "title": "kuakua | 正念每日问题思考与聚焦专注 V1.6 发布啦！ [浏览器插件]"}, {"id": 117, "title": "分享一个用 AI 帮我自动整理待办事项的网站 · 求建议"}, {"id": 118, "title": "tutuicmptunnel - 基于 bpf 的 udp 转换为 ICMP 隧道工具"}, {"id": 119, "title": "开源免费、适合中小团队使用的 AI 聊天机器人： HiveChat，一人配置，全员使用，已上线 Cloud 版"}, {"id": 120, "title": "How To Say - 三种智能模式多语言表达神器！表达/翻译/理解一键切换，多种语言快速生成，无需登录免费用！"}, {"id": 121, "title": "宣传一下 RIFE 视频插帧 / 补帧方案"}, {"id": 122, "title": "🚀 用 Rust + OpenAPI 写接口的人看过来！请求校验不用再手写了"}, {"id": 123, "title": "业余开发一年多，我是如何给策引做一个 DSL 策略引擎的"}, {"id": 124, "title": "都在聊币，十分钟用 hammerspoon 做了一个菜单栏看币价的插件"}, {"id": 125, "title": "[送码] 视频播放器 APP 上架了，现在准备干翻 Infuse"}, {"id": 126, "title": "分享刚做的可打印的血压记录工具"}, {"id": 127, "title": "自己开发了一套跨厂商设备协作工具（支持 Android/Windows/iOS/macOS），欢迎大家试用并反馈意见～"}, {"id": 128, "title": "Grow a Garden 老玩家，开发了个必备工具站 GAG Caulator Roblox"}, {"id": 129, "title": "开发了一个开源/端到端加密的 todo 软件"}, {"id": 130, "title": "[💯自荐] Windows 终于有了好用的下载器 ～ 免费无限制"}, {"id": 131, "title": "搞不好有一天开始有人在 v 站更小说、卖教程、卖服务、甚至发任务？哈哈，感觉会很有意思"}, {"id": 132, "title": "写了个查看 V2EX 金币趋势的插件"}, {"id": 133, "title": "[送码-iOS] 便便记录器，关注肠道健康"}, {"id": 134, "title": "Nexty - 适用于多种场景的 Next.js SaaS 模板 (送 5 折优惠码)"}, {"id": 135, "title": "搞了一个地图社交 web"}, {"id": 136, "title": "第二个 IOS APP 上线，汇率换算"}, {"id": 137, "title": "[内测招募] CelerGPT 基于 Flux.1 Kontext 和 GPT-4o 的 AI 图像生成网站，邀请您免费体验"}, {"id": 138, "title": "不是我的产品，突然发现一个好用的类似 postman 工具"}, {"id": 139, "title": "wr.do 支持文件存储了（兼容 S3，可集成 R2、OSS、COS、AWS S3 多个平台）"}, {"id": 140, "title": "市面上有没有像绿巨能六边形定时器配 app 的番茄钟？"}, {"id": 141, "title": "鉴于 https://r1mix.ai/ 停止服务，用 golang 构建个一样的服务，满足自己 SOTA 级混合模型使用需求"}, {"id": 142, "title": "写了一个 vitepress 插件，集成了 Markmap 和 Mermaid 图表预览功能，为 Markdown 文档提供增强的图表支持。"}, {"id": 143, "title": "[开源 VSCode 插件] 堪称 Claude Code 最佳伴侣，一键复制报错+多文件地址+上下文"}, {"id": 144, "title": "造了一个新的轮子 super-browser-window-kit , 用来给 Electron app 适配 macOS 26 Tahoe , 支持窗口大圆角和液态玻璃背景"}, {"id": 145, "title": "I made an \"Indian Dowry Calculator\" to find out how much you are worth?"}, {"id": 146, "title": "基于大语言模型的文本隐写术构想"}, {"id": 147, "title": "纯代码小白，花了两个月才做出来的 AI 心理网站，求鼓励"}, {"id": 148, "title": "Fast3D - 无需登录即可无限使用 3D 模型生成网站！输入本字或图片，一键转 3D 模型， 8 秒完成！"}]