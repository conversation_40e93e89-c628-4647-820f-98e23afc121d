[{"keyword": "AI", "aliases": ["人工智能", "生成式AI"], "ids": [102, 103, 117, 119, 120, 137, 146, 147]}, {"keyword": "开源", "aliases": ["opensource", "源码公开"], "ids": [100, 103, 119, 129, 143]}, {"keyword": "浏览器插件", "aliases": ["浏览器扩展", "extension"], "ids": [101, 116]}, {"keyword": "Midjourney", "aliases": ["MJ", "Midjourney模型"], "ids": [104]}, {"keyword": "<PERSON><PERSON><PERSON><PERSON>", "aliases": ["向量数据库", "GPT知识库"], "ids": [103]}, {"keyword": "Solana", "aliases": ["Sol地址", "Sol"], "ids": [105]}, {"keyword": "Discourse", "aliases": ["论坛", "社区"], "ids": [106]}, {"keyword": "<PERSON>er", "aliases": ["容器", "docker registry"], "ids": [107]}, {"keyword": "网盘", "aliases": ["百度网盘", "夸克网盘"], "ids": [109]}, {"keyword": "V2EX", "aliases": ["v 站", "v站"], "ids": [111, 131, 132]}, {"keyword": "Go", "aliases": ["Golang"], "ids": [112]}, {"keyword": "macOS", "aliases": ["<PERSON>", "mac"], "ids": [113, 127, 144]}, {"keyword": "Rust", "aliases": [], "ids": [122]}, {"keyword": "DSL", "aliases": ["策略引擎"], "ids": [123]}, {"keyword": "Hammerspoon", "aliases": ["菜单栏插件"], "ids": [124]}, {"keyword": "时钟", "aliases": ["桌面时钟"], "ids": [113]}, {"keyword": "下载器", "aliases": ["下载工具"], "ids": [130]}, {"keyword": "健康", "aliases": ["肠道健康", "血压记录"], "ids": [126, 133]}, {"keyword": "Next.js", "aliases": ["Nexty", "SaaS模板"], "ids": [134]}, {"keyword": "汇率换算", "aliases": ["汇率", "换算器"], "ids": [136]}, {"keyword": "Postman", "aliases": ["API工具"], "ids": [138]}, {"keyword": "S3", "aliases": ["文件存储", "对象存储"], "ids": [139]}, {"keyword": "番茄钟", "aliases": ["定时器", "Pomodoro"], "ids": [140]}, {"keyword": "Golang", "aliases": ["Go"], "ids": [141]}, {"keyword": "VitePress", "aliases": ["Markdown增强", "图表插件"], "ids": [142]}, {"keyword": "VSCode 插件", "aliases": ["VSCode扩展"], "ids": [143]}, {"keyword": "Electron", "aliases": ["桌面应用", "macOS UI"], "ids": [144]}, {"keyword": "3D模型", "aliases": ["3D生成", "模型生成"], "ids": [148]}]