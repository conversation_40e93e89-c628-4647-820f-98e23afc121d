import React from 'react'
import { FileText, X } from 'lucide-react'

interface DetailPanelProps {
  hoveredWord: string | null
  hoveredPosts: { id: number; title: string }[] | null
  onPostClick?: (post: { id: number; title: string }) => void
  onClose?: () => void
  isFixed?: boolean
}

const DetailPanel: React.FC<DetailPanelProps> = ({
  hoveredWord,
  hoveredPosts,
  onPostClick,
  onClose,
  isFixed = false
}) => {
  if (!hoveredPosts || hoveredPosts.length === 0 || !hoveredWord) {
    return null
  }

  return (
    <div className={`bg-white rounded-lg shadow-lg w-96 max-h-96 overflow-hidden transition-all duration-200 ${
      isFixed ? 'border-2 border-solid border-gray-200' : 'border-2 border-dashed border-gray-300'
    }`}>
      <div className="flex items-center justify-between px-3 py-2 border-b border-gray-100 min-h-[44px]">
        <div className="flex items-center gap-2">
          <FileText className="w-4 h-4 text-green-500" />
          <span className="text-sm font-medium text-gray-700">
            详情面板 - "{hoveredWord}" 相关帖子 ({hoveredPosts.length})
          </span>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded transition-colors"
            title="关闭详情面板"
          >
            <X className="w-4 h-4 text-gray-500" />
          </button>
        )}
      </div>
      <div className="px-3 py-2 max-h-80 overflow-y-auto space-y-1">
        {hoveredPosts.map((post) => (
          <div 
            key={post.id} 
            className="text-sm text-gray-600 hover:text-blue-600 cursor-pointer px-2 py-1.5 hover:bg-gray-50 rounded transition-colors border border-gray-100 leading-tight"
            onClick={() => {
              console.log('点击帖子:', post)
              onPostClick?.(post)
            }}
          >
            {post.title}
          </div>
        ))}
      </div>
    </div>
  )
}

export default DetailPanel