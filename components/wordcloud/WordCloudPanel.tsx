import React, { useState, useEffect, useRef } from 'react'
import { ChevronDown, ChevronUp, Tag } from 'lucide-react'
import WordCloudComponent from './WordCloud'
import DetailPanel from './DetailPanel'
import { loadDemoData, getTopKeywords } from '../../lib/wordcloudUtils'
import { detectPostLists, highlightDetectedPosts } from '../../lib/postDetection'

interface WordCloudItem {
  text: string
  size: number
  relatedPosts?: { id: number; title: string }[]
}

const WordCloudPanel: React.FC = () => {
  const [isExpanded, setIsExpanded] = useState(false)
  const [wordCloudData, setWordCloudData] = useState<WordCloudItem[]>([])
  const [loading, setLoading] = useState(true)
  const [hoveredPosts, setHoveredPosts] = useState<{ id: number; title: string }[] | null>(null)
  const [hoveredWord, setHoveredWord] = useState<string | null>(null)
  const [currentViewingWord, setCurrentViewingWord] = useState<string | null>(null)
  const [currentViewingPosts, setCurrentViewingPosts] = useState<{ id: number; title: string }[] | null>(null)
  const [debugHighlight, setDebugHighlight] = useState(false)
  const hideTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true)
        const { keywords, titles } = await loadDemoData()
        console.log('Loaded keywords:', keywords.length, 'titles:', titles.length)
        const topKeywords = getTopKeywords(keywords, 15, titles)
        console.log('Top keywords:', topKeywords)
        setWordCloudData(topKeywords)
      } catch (error) {
        console.error('Failed to load word cloud data:', error)
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [])

  // 清理定时器
  useEffect(() => {
    return () => {
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current)
      }
    }
  }, [])

  const handleWordHover = (word: string | null, posts?: { id: number; title: string }[], event?: MouseEvent) => {
    // 如果有固定查看的词，不响应悬停事件
    if (currentViewingWord) {
      return
    }

    // 清除之前的隐藏定时器
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current)
      hideTimeoutRef.current = null
    }

    if (word && posts) {
      // 显示帖子列表
      setHoveredWord(word)
      setHoveredPosts(posts)
    } else {
      // 延迟隐藏帖子列表
      hideTimeoutRef.current = setTimeout(() => {
        setHoveredWord(null)
        setHoveredPosts(null)
      }, 150) // 150ms延迟
    }
  }

  const handleWordClick = (word: string, posts?: { id: number; title: string }[]) => {
    console.log('点击关键词:', word, '相关帖子:', posts)
    
    if (currentViewingWord === word) {
      // 如果点击的是当前查看的词，取消固定显示
      setCurrentViewingWord(null)
      setCurrentViewingPosts(null)
    } else {
      // 清除悬停定时器
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current)
        hideTimeoutRef.current = null
      }
      
      // 先设置新的固定查看词，然后清除悬停状态
      // 这样确保DetailPanel始终有数据显示
      setCurrentViewingWord(word)
      setCurrentViewingPosts(posts || [])
      
      // 延迟清除悬停状态，确保新的固定状态已经生效
      setTimeout(() => {
        setHoveredWord(null)
        setHoveredPosts(null)
      }, 0)
    }
  }

  const handleDetailPanelClose = () => {
    // 关闭详情面板
    setCurrentViewingWord(null)
    setCurrentViewingPosts(null)
  }

  const handlePanelToggle = () => {
    const newExpandedState = !isExpanded
    setIsExpanded(newExpandedState)
    
    // 如果是展开操作，则触发帖子列表检测
    if (newExpandedState) {
      console.log('[词云面板] 面板展开，开始检测网页帖子列表...')
      
      // 延迟执行检测，确保面板动画完成
      setTimeout(() => {
        try {
          const clusters = detectPostLists()
          
          if (clusters.length > 0) {
            console.log(`[词云面板] 检测到 ${clusters.length} 个帖子列表聚类`)
            
                         // 可选：高亮显示检测到的帖子（用于调试）
             if (debugHighlight) {
               highlightDetectedPosts(clusters)
               console.log('[词云面板] 已启用调试高亮显示')
             }
            
            // 输出检测结果摘要
            console.group('[词云面板] 帖子检测结果摘要')
            clusters.forEach((cluster, index) => {
              console.log(`聚类 ${index + 1}: ${cluster.elements.length} 个帖子，置信度 ${(cluster.confidence * 100).toFixed(1)}%`)
              if (cluster.posts.length > 0 && cluster.posts[0].title) {
                console.log(`  示例标题: "${cluster.posts[0].title}"`)
              }
            })
            console.groupEnd()
          } else {
            console.log('[词云面板] 未检测到明显的帖子列表结构')
          }
        } catch (error) {
          console.error('[词云面板] 帖子检测过程中出现错误:', error)
        }
      }, 300) // 300ms延迟，等待展开动画完成
    }
  }

  return (
    <div className="fixed top-4 right-4 z-[10000] transition-all duration-300 ease-in-out">
      <div className="flex gap-3">
        {/* 详情面板 */}
        {isExpanded && (currentViewingWord || hoveredWord) && (
          <DetailPanel
            hoveredWord={currentViewingWord || hoveredWord}
            hoveredPosts={currentViewingPosts || hoveredPosts}
            onPostClick={(post) => console.log('点击帖子:', post)}
            onClose={currentViewingWord ? handleDetailPanelClose : undefined}
            isFixed={!!currentViewingWord}
          />
        )}
        
        {/* 词云面板 */}
        <div
          className="bg-white border border-gray-200 rounded-lg shadow-lg transition-all duration-300 ease-in-out"
          style={{
            width: isExpanded ? '450px' : '200px',
            maxHeight: isExpanded ? '500px' : '50px',
            overflow: 'hidden'
          }}
        >
          {/* 标题栏 */}
          <div
            className="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50 transition-colors"
            onClick={handlePanelToggle}
          >
            <div className="flex items-center gap-2">
              <Tag className="w-4 h-4 text-blue-500" />
              <span 
                className="text-sm font-medium text-gray-700"
                onDoubleClick={(e) => {
                  e.stopPropagation()
                  setDebugHighlight(!debugHighlight)
                  console.log(`[词云面板] 调试高亮模式: ${!debugHighlight ? '已启用' : '已禁用'}`)
                }}
                title={debugHighlight ? "双击关闭调试高亮" : "双击启用调试高亮"}
              >
                关键词词云{debugHighlight && ' 🔍'}
              </span>
            </div>
            {isExpanded ? (
              <ChevronUp className="w-4 h-4 text-gray-500" />
            ) : (
              <ChevronDown className="w-4 h-4 text-gray-500" />
            )}
          </div>

          {/* 词云内容 */}
          {isExpanded && (
            <div className="p-3 pt-0">
              {loading ? (
                <div className="flex items-center justify-center h-32">
                  <div className="text-sm text-gray-500">加载中...</div>
                </div>
              ) : wordCloudData.length > 0 ? (
                <div className="space-y-3">
                  <WordCloudComponent
                    words={wordCloudData}
                    width={400}
                    height={300}
                    minFontSize={15}
                    maxFontSize={60}
                    onWordHover={handleWordHover}
                    onWordClick={handleWordClick}
                  />
                  
                  <div className="text-xs text-gray-500 text-center">
                    共 {wordCloudData.length} 个热门关键词
                    {hoveredWord && (
                      <span className="ml-2 text-blue-600">• 悬停: {hoveredWord}</span>
                    )}
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-32">
                  <div className="text-sm text-gray-500">暂无数据</div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default WordCloudPanel