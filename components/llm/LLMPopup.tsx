import React, { useState } from 'react'
import { useAppDispatch, useAppSelector } from '~store/store'
import { initializeLLM, generateResponse } from '~store/llmSlice'
import { Button } from '~components/ui/button'
import { Input } from '~components/ui/input'
import { Loader2, Send, Bot, MessageSquare } from 'lucide-react'

const LLMPopup: React.FC = () => {
  const dispatch = useAppDispatch()
  const {
    isLoading,
    isInitialized,
    error,
    currentModel,
    availableModels,
    chatHistory,
    isGenerating
  } = useAppSelector((state) => state.llm)
  
  const [selectedModel, setSelectedModel] = useState(availableModels[0])
  const [inputMessage, setInputMessage] = useState('')
  const [showQuickInit, setShowQuickInit] = useState(!isInitialized)

  const handleQuickInit = async () => {
    dispatch(initializeLLM(selectedModel))
    setShowQuickInit(false)
  }

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || !isInitialized || isGenerating) return
    
    const message = inputMessage.trim()
    setInputMessage('')
    dispatch(generateResponse(message))
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const openSidePanelForChat = () => {
    chrome.sidePanel.setOptions({ enabled: true })
    chrome.tabs.query({ active: true, currentWindow: true }, ([tab]) => {
      chrome.sidePanel.open({ tabId: tab.id })
    })
  }

  const lastAssistantMessage = chatHistory
    .filter(msg => msg.role === 'assistant')
    .slice(-1)[0]

  return (
    <div className="space-y-3">
      {/* 快速初始化 */}
      {showQuickInit && !isInitialized && (
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm">
            <Bot className="w-4 h-4" />
            <span>Web-LLM 大模型</span>
          </div>
          <select
            value={selectedModel}
            onChange={(e) => setSelectedModel(e.target.value)}
            disabled={isLoading}
            className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-600"
          >
            {availableModels.map((model) => (
              <option key={model} value={model}>
                {model.replace('-MLC', '').replace('q4f32_1', '').replace('q4f16_1', '')}
              </option>
            ))}
          </select>
          <Button
            onClick={handleQuickInit}
            disabled={isLoading}
            size="sm"
            className="w-full"
          >
            {isLoading ? (
              <>
                <Loader2 className="w-3 h-3 mr-2 animate-spin" />
                初始化中...
              </>
            ) : (
              '初始化AI助手'
            )}
          </Button>
        </div>
      )}

      {/* 错误提示 */}
      {error && (
        <div className="text-xs text-red-600 dark:text-red-400 p-2 bg-red-50 dark:bg-red-900/20 rounded">
          {error}
        </div>
      )}

      {/* 已初始化状态 */}
      {isInitialized && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-xs text-green-600 dark:text-green-400">
              <Bot className="w-3 h-3" />
              <span>AI助手已就绪</span>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={openSidePanelForChat}
              className="text-xs px-2 py-1 h-6"
            >
              <MessageSquare className="w-3 h-3 mr-1" />
              完整对话
            </Button>
          </div>

          {/* 最后一条AI回复预览 */}
          {lastAssistantMessage && (
            <div className="text-xs p-2 bg-gray-50 dark:bg-gray-800 rounded border">
              <div className="text-gray-600 dark:text-gray-400 mb-1">最近回复:</div>
              <div className="line-clamp-2">
                {lastAssistantMessage.content.length > 100
                  ? lastAssistantMessage.content.substring(0, 100) + '...'
                  : lastAssistantMessage.content}
              </div>
            </div>
          )}

          {/* 快速提问 */}
          <div className="flex gap-1">
            <Input
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="快速提问..."
              disabled={isGenerating}
              className="flex-1 text-xs h-8"
            />
            <Button
              onClick={handleSendMessage}
              disabled={!inputMessage.trim() || isGenerating}
              size="sm"
              className="px-2 h-8"
            >
              {isGenerating ? (
                <Loader2 className="w-3 h-3 animate-spin" />
              ) : (
                <Send className="w-3 h-3" />
              )}
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}

export default LLMPopup