import React, { useState, useRef, useEffect } from 'react'
import { useAppDispatch, useAppSelector } from '~store/store'
import { initializeLLM, generateResponse, clearChatHistory, clearError } from '~store/llmSlice'
import { Button } from '~components/ui/button'
import { Input } from '~components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '~components/ui/card'
import { Loader2, Send, Trash2, Bo<PERSON>, User, AlertCircle } from 'lucide-react'

const LLMChat: React.FC = () => {
  const dispatch = useAppDispatch()
  const {
    isLoading,
    isInitialized,
    error,
    currentModel,
    availableModels,
    chatHistory,
    isGenerating
  } = useAppSelector((state) => state.llm)
  
  const [selectedModel, setSelectedModel] = useState(availableModels[0])
  const [inputMessage, setInputMessage] = useState('')
  const chatEndRef = useRef<HTMLDivElement>(null)

  // 自动滚动到底部
  useEffect(() => {
    chatEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [chatHistory])

  const handleInitialize = async () => {
    if (selectedModel) {
      dispatch(initializeLLM(selectedModel))
    }
  }

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || !isInitialized || isGenerating) return
    
    const message = inputMessage.trim()
    setInputMessage('')
    dispatch(generateResponse(message))
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const handleClearChat = () => {
    dispatch(clearChatHistory())
  }

  const handleClearError = () => {
    dispatch(clearError())
  }

  return (
    <div className="w-full max-w-4xl mx-auto p-4 space-y-4">
      {/* 模型选择和初始化 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bot className="w-5 h-5" />
            Web-LLM 大模型
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-2">
            <select
              value={selectedModel}
              onChange={(e) => setSelectedModel(e.target.value)}
              disabled={isLoading || isInitialized}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-600"
            >
              {availableModels.map((model) => (
                <option key={model} value={model}>
                  {model}
                </option>
              ))}
            </select>
            <Button
              onClick={handleInitialize}
              disabled={isLoading || isInitialized}
              className="min-w-[100px]"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  初始化中...
                </>
              ) : isInitialized ? (
                '已初始化'
              ) : (
                '初始化模型'
              )}
            </Button>
          </div>
          
          {isInitialized && (
            <div className="text-sm text-green-600 dark:text-green-400">
              ✓ 模型 {currentModel} 已就绪
            </div>
          )}
        </CardContent>
      </Card>

      {/* 错误提示 */}
      {error && (
        <Card className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20">
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
                <AlertCircle className="w-4 h-4" />
                <span>{error}</span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleClearError}
                className="text-red-600 border-red-300 hover:bg-red-100 dark:text-red-400 dark:border-red-700 dark:hover:bg-red-900/30"
              >
                关闭
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 聊天界面 */}
      {isInitialized && (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>聊天对话</CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={handleClearChat}
              disabled={chatHistory.length === 0}
            >
              <Trash2 className="w-4 h-4 mr-2" />
              清空对话
            </Button>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* 聊天历史 */}
            <div className="h-96 overflow-y-auto border rounded-lg p-4 space-y-4 bg-gray-50 dark:bg-gray-900">
              {chatHistory.length === 0 ? (
                <div className="text-center text-gray-500 dark:text-gray-400 py-8">
                  开始与AI助手对话吧！
                </div>
              ) : (
                chatHistory.map((message) => (
                  <div
                    key={message.id}
                    className={`flex gap-3 ${
                      message.role === 'user' ? 'justify-end' : 'justify-start'
                    }`}
                  >
                    <div
                      className={`flex gap-2 max-w-[80%] ${
                        message.role === 'user' ? 'flex-row-reverse' : 'flex-row'
                      }`}
                    >
                      <div className="flex-shrink-0">
                        {message.role === 'user' ? (
                          <User className="w-6 h-6 p-1 bg-blue-500 text-white rounded-full" />
                        ) : (
                          <Bot className="w-6 h-6 p-1 bg-green-500 text-white rounded-full" />
                        )}
                      </div>
                      <div
                        className={`px-4 py-2 rounded-lg ${
                          message.role === 'user'
                            ? 'bg-blue-500 text-white'
                            : 'bg-white dark:bg-gray-800 border'
                        }`}
                      >
                        <div className="whitespace-pre-wrap">{message.content}</div>
                        <div className="text-xs opacity-70 mt-1">
                          {new Date(message.timestamp).toLocaleTimeString()}
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              )}
              {isGenerating && (
                <div className="flex gap-3 justify-start">
                  <div className="flex gap-2 max-w-[80%]">
                    <Bot className="w-6 h-6 p-1 bg-green-500 text-white rounded-full flex-shrink-0" />
                    <div className="px-4 py-2 rounded-lg bg-white dark:bg-gray-800 border">
                      <div className="flex items-center gap-2">
                        <Loader2 className="w-4 h-4 animate-spin" />
                        <span>AI正在思考中...</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              <div ref={chatEndRef} />
            </div>

            {/* 输入框 */}
            <div className="flex gap-2">
              <Input
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="输入您的问题..."
                disabled={isGenerating}
                className="flex-1"
              />
              <Button
                onClick={handleSendMessage}
                disabled={!inputMessage.trim() || isGenerating}
                size="icon"
              >
                <Send className="w-4 h-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

export default LLMChat