interface KeywordData {
  keyword: string
  aliases: string[]
  ids: number[]
}

interface TitleData {
  id: number
  title: string
}

interface WordCloudItem {
  text: string
  size: number
  relatedPosts?: { id: number; title: string }[]
}

/**
 * 处理关键词数据，生成词云数据
 * @param keywords 关键词数据
 * @param titles 标题数据（可选，用于获取关联帖子信息）
 * @returns 词云数据数组
 */
export function processKeywordsForWordCloud(
  keywords: KeywordData[],
  titles?: TitleData[]
): WordCloudItem[] {
  // 计算每个关键词的权重（基于关联的文章数量）
  const wordCloudData: WordCloudItem[] = keywords.map(item => {
    const weight = item.ids.length
    
    // 获取关联的帖子信息
    const relatedPosts = titles ? 
      item.ids.map(id => {
        const title = titles.find(t => t.id === id)
        return title ? { id: title.id, title: title.title } : null
      }).filter(Boolean) as { id: number; title: string }[] : undefined
    
    // 使用更合理的字体大小计算，确保有足够的差异
    return {
      text: item.keyword,
      size: Math.max(weight * 5 + 10, 15), // 基础大小15，每个关联文章增加5
      relatedPosts
    }
  })

  // 按权重排序，权重高的在前
  wordCloudData.sort((a, b) => b.size - a.size)

  return wordCloudData
}

/**
 * 获取热门关键词（前N个）
 * @param keywords 关键词数据
 * @param limit 限制数量，默认20
 * @param titles 标题数据（可选，用于获取关联帖子信息）
 * @returns 热门关键词数组
 */
export function getTopKeywords(
  keywords: KeywordData[],
  limit: number = 20,
  titles?: TitleData[]
): WordCloudItem[] {
  const wordCloudData = processKeywordsForWordCloud(keywords, titles)
  return wordCloudData.slice(0, limit)
}

/**
 * 加载demo数据
 * @returns Promise包含关键词和标题数据
 */
export async function loadDemoData(): Promise<{
  keywords: KeywordData[]
  titles: TitleData[]
}> {
  try {
    // 在浏览器扩展中，我们需要使用chrome.runtime.getURL来获取资源
    const keywordsUrl = chrome.runtime.getURL('demo_data/keywords.json')
    const titlesUrl = chrome.runtime.getURL('demo_data/titles.json')

    const [keywordsResponse, titlesResponse] = await Promise.all([
      fetch(keywordsUrl),
      fetch(titlesUrl)
    ])

    const keywords = await keywordsResponse.json()
    const titles = await titlesResponse.json()

    return { keywords, titles }
  } catch (error) {
    console.error('Failed to load demo data:', error)
    return { keywords: [], titles: [] }
  }
}