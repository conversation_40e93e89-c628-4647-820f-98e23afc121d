import { useState } from "react"
import { Provider } from "react-redux"
import { PersistGate } from "@plasmohq/redux-persist/integration/react"
import { persistor, store } from "~store/store"
import { Button } from "./components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "./components/ui/card"
import LLMChat from "~components/llm/LLMChat"
import "./style.css"

function OptionsIndex() {
  const [activeTab, setActiveTab] = useState<"general" | "llm">("llm")

  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
          <div className="w-full max-w-6xl mx-auto p-6">
            <h1 className="text-3xl font-bold mb-6 text-gray-900 dark:text-white">
              BrowseForMe 扩展设置
            </h1>
            
            {/* 标签导航 */}
            <div className="flex border-b mb-6">
              <button
                className={`py-3 px-6 font-medium ${
                  activeTab === "general"
                    ? "border-b-2 border-blue-500 text-blue-600"
                    : "text-gray-500 hover:text-gray-700"
                }`}
                onClick={() => setActiveTab("general")}
              >
                通用设置
              </button>
              <button
                className={`py-3 px-6 font-medium ${
                  activeTab === "llm"
                    ? "border-b-2 border-blue-500 text-blue-600"
                    : "text-gray-500 hover:text-gray-700"
                }`}
                onClick={() => setActiveTab("llm")}
              >
                AI助手
              </button>
            </div>
            
            {/* 通用设置 */}
            {activeTab === "general" && (
              <Card>
                <CardHeader>
                  <CardTitle>通用设置</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">扩展配置:</label>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        这里可以添加其他扩展相关的配置选项。
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
            
            {/* LLM设置 */}
            {activeTab === "llm" && (
              <LLMChat />
            )}
          </div>
        </div>
      </PersistGate>
    </Provider>
  )
}

export default OptionsIndex