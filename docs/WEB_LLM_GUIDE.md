# Web-LLM 大模型支持指南

## 功能概述

本扩展现已集成 Web-LLM 支持，可以在浏览器中直接运行大语言模型，无需服务器支持。

## 支持的模型

- **Llama-3.2-3B-Instruct** - 高质量对话模型
- **Llama-3.2-1B-Instruct** - 轻量级对话模型
- **Phi-3.5-mini-instruct** - 微软开发的小型模型
- **gemma-2-2b-it** - Google 开发的轻量级模型

## 使用方法

### 1. 在弹出窗口中使用

1. 点击扩展图标打开弹出窗口
2. 切换到「AI助手」标签页
3. 选择要使用的模型
4. 点击「初始化AI助手」按钮
5. 等待模型下载和初始化完成
6. 在输入框中输入问题，按回车或点击发送按钮

### 2. 在侧边栏中使用

1. 在弹出窗口的「通用操作」标签页中点击「打开侧边栏」
2. 或者在AI助手初始化后点击「完整对话」按钮
3. 在侧边栏中可以进行完整的对话体验
4. 支持查看完整的聊天历史
5. 可以清空对话历史重新开始

### 3. 在选项页面中使用

1. 右键点击扩展图标，选择「选项」
2. 切换到「AI助手」标签页
3. 享受完整的AI对话体验

## 技术特性

### 本地运行
- 所有模型都在浏览器本地运行
- 无需网络连接进行推理
- 保护用户隐私，数据不会上传到服务器

### 性能优化
- 使用 WebAssembly 和 WebGPU 加速
- 支持量化模型，减少内存占用
- 首次使用需要下载模型文件（约1-3GB）

### 状态管理
- 使用 Redux 管理应用状态
- 支持对话历史持久化
- 跨页面状态同步

## 注意事项

### 系统要求
- 现代浏览器（Chrome 88+）
- 至少 4GB 可用内存
- 支持 WebAssembly 和 WebGPU

### 首次使用
- 首次初始化模型需要下载较大文件
- 建议在稳定的网络环境下进行
- 下载完成后，后续使用无需网络

### 性能建议
- 较大的模型（如 Llama-3.2-3B）提供更好的对话质量
- 较小的模型（如 Llama-3.2-1B）启动更快，占用内存更少
- 根据设备性能选择合适的模型

## 故障排除

### 初始化失败
- 检查网络连接
- 确保浏览器支持 WebAssembly
- 尝试刷新页面重新初始化

### 内存不足
- 关闭其他标签页释放内存
- 选择更小的模型
- 重启浏览器

### 响应缓慢
- 检查设备性能
- 尝试使用更小的模型
- 确保没有其他高负载应用运行

## 开发信息

### 技术栈
- **@mlc-ai/web-llm**: Web-LLM 核心库
- **React + TypeScript**: 用户界面
- **Redux Toolkit**: 状态管理
- **Tailwind CSS**: 样式框架
- **Plasmo**: 浏览器扩展框架

### 文件结构
```
store/
  └── llmSlice.ts          # LLM 状态管理
components/
  └── llm/
      ├── LLMChat.tsx      # 完整聊天界面
      └── LLMPopup.tsx     # 弹出窗口界面
```

### 自定义配置
可以在 `llmSlice.ts` 中修改：
- 可用模型列表
- 默认模型参数
- 聊天历史长度限制

## 更新日志

### v0.0.1
- ✅ 集成 Web-LLM 支持
- ✅ 支持多种预训练模型
- ✅ 弹出窗口快速对话
- ✅ 侧边栏完整对话体验
- ✅ 选项页面配置界面
- ✅ 对话历史管理
- ✅ 错误处理和状态提示