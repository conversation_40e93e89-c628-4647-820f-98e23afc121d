import { createSlice, createAsyncThunk, type PayloadAction } from '@reduxjs/toolkit'
import { CreateMLCEngine, type MLCEngineInterface } from '@mlc-ai/web-llm'

export interface LLMState {
  engine: MLCEngineInterface | null
  isLoading: boolean
  isInitialized: boolean
  error: string | null
  currentModel: string | null
  availableModels: string[]
  chatHistory: Array<{
    id: string
    role: 'user' | 'assistant'
    content: string
    timestamp: number
  }>
  isGenerating: boolean
}

const initialState: LLMState = {
  engine: null,
  isLoading: false,
  isInitialized: false,
  error: null,
  currentModel: null,
  availableModels: [
    'Llama-3.2-3B-Instruct-q4f32_1-MLC',
    'Llama-3.2-1B-Instruct-q4f32_1-MLC',
    'Phi-3.5-mini-instruct-q4f16_1-MLC',
    'gemma-2-2b-it-q4f16_1-MLC'
  ],
  chatHistory: [],
  isGenerating: false
}

// 异步thunk：初始化LLM引擎
export const initializeLLM = createAsyncThunk(
  'llm/initialize',
  async (modelId: string, { rejectWithValue }) => {
    try {
      const engine = await CreateMLCEngine(modelId, {
        initProgressCallback: (progress) => {
          console.log('LLM初始化进度:', progress)
        }
      })
      return { engine, modelId }
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : '初始化失败')
    }
  }
)

// 异步thunk：生成回复
export const generateResponse = createAsyncThunk(
  'llm/generateResponse',
  async (message: string, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { llm: LLMState }
      const { engine, chatHistory } = state.llm
      
      if (!engine) {
        throw new Error('LLM引擎未初始化')
      }

      // 构建消息历史
      const messages = [
        ...chatHistory.map(msg => ({
          role: msg.role,
          content: msg.content
        })),
        { role: 'user' as const, content: message }
      ]

      const response = await engine.chat.completions.create({
        messages,
        temperature: 0.7,
        max_tokens: 1000
      })

      const assistantMessage = response.choices[0]?.message?.content || '抱歉，我无法生成回复。'
      
      return {
        userMessage: message,
        assistantMessage,
        timestamp: Date.now()
      }
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : '生成回复失败')
    }
  }
)

const llmSlice = createSlice({
  name: 'llm',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    clearChatHistory: (state) => {
      state.chatHistory = []
    },
    addUserMessage: (state, action: PayloadAction<string>) => {
      state.chatHistory.push({
        id: Date.now().toString(),
        role: 'user',
        content: action.payload,
        timestamp: Date.now()
      })
    },
    setCurrentModel: (state, action: PayloadAction<string>) => {
      state.currentModel = action.payload
    }
  },
  extraReducers: (builder) => {
    builder
      // 初始化LLM
      .addCase(initializeLLM.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(initializeLLM.fulfilled, (state, action) => {
        state.isLoading = false
        state.isInitialized = true
        state.engine = action.payload.engine
        state.currentModel = action.payload.modelId
      })
      .addCase(initializeLLM.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload as string
      })
      // 生成回复
      .addCase(generateResponse.pending, (state) => {
        state.isGenerating = true
        state.error = null
      })
      .addCase(generateResponse.fulfilled, (state, action) => {
        state.isGenerating = false
        // 添加用户消息
        state.chatHistory.push({
          id: `user-${action.payload.timestamp}`,
          role: 'user',
          content: action.payload.userMessage,
          timestamp: action.payload.timestamp
        })
        // 添加助手回复
        state.chatHistory.push({
          id: `assistant-${action.payload.timestamp}`,
          role: 'assistant',
          content: action.payload.assistantMessage,
          timestamp: action.payload.timestamp + 1
        })
      })
      .addCase(generateResponse.rejected, (state, action) => {
        state.isGenerating = false
        state.error = action.payload as string
      })
  }
})

export const { clearError, clearChatHistory, addUserMessage, setCurrentModel } = llmSlice.actions
export default llmSlice.reducer